import { useState, useEffect } from "react";
import authService from "../services/authService";
import bloodDonationService from "../services/bloodDonationService";
import NotificationService from "../services/notificationService";
import { DOCTOR_TYPES } from "../constants/systemConstants";
import { toast } from "../utils/toastUtils";

/**
 * Custom hook for managing doctor donor management logic
 */
export const useDoctorDonorManagement = () => {
  const [donors, setDonors] = useState([]);
  const [loading, setLoading] = useState(true);
  const [filters, setFilters] = useState({
    status: "all",
    bloodType: "all",
    process: "all",
    timeSlot: "all"
  });
  const [selectedDonor, setSelectedDonor] = useState(null);
  const [showUpdateModal, setShowUpdateModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);

  const [updateData, setUpdateData] = useState({
    bloodType: "",
    healthStatus: "",
    chronicDiseases: [],
    bloodRelatedDiseases: [],
    notes: "",
    bloodPressure: "",
    heartRate: 0,
    hemoglobin: 0,
    temperature: 0,
    weight: 0,
    height: 0,
    process: 2,
    status: false,
    donationCapacity: 0,
    // Blood test results
    bloodGroup: "",
    rhType: "",
    donationDate: "",
    testResults: {
      hemoglobin: "",
      bloodPressure: "",
      heartRate: "",
      temperature: "",
      weight: "",
      height: "",
    },
  });

  const [statusUpdateData, setStatusUpdateData] = useState({
    status: "",
    process: "",
    notes: "",
    bloodPressure: "",
    heartRate: 0,
    hemoglobin: 0,
    temperature: 0,
    weight: 0,
    height: 0,
    donationCapacity: 0,
  });

  const currentUser = authService.getCurrentUser();
  const isBloodDepartment = currentUser?.doctorType === DOCTOR_TYPES.BLOOD_DEPARTMENT;

  // Load donors data
  const loadDonors = async () => {
    setLoading(true);
    try {
      const response = await bloodDonationService.getAllAppointments();

      // Debug: Log raw API response to check Cancel field
      console.log('Raw API response:', response);
      if (Array.isArray(response) && response.length > 0) {
        console.log('Sample appointment from API:', response[0]);

        // Check for any cancel-related fields in the API response
        const sampleAppointment = response[0];
        const cancelFields = {};
        Object.keys(sampleAppointment).forEach(key => {
          if (key.toLowerCase().includes('cancel')) {
            cancelFields[key] = sampleAppointment[key];
          }
        });
        console.log('Cancel-related fields in API:', cancelFields);

        // Log all appointments with any cancel-related fields
        const appointmentsWithCancel = response.filter(apt => {
          return Object.keys(apt).some(key =>
            key.toLowerCase().includes('cancel') && apt[key] !== null && apt[key] !== undefined
          );
        });
        console.log('Appointments with cancel fields:', appointmentsWithCancel.length, appointmentsWithCancel);
      }

      let appointmentsData = [];
      if (Array.isArray(response)) {
        appointmentsData = response;
      } else if (response.data && Array.isArray(response.data)) {
        appointmentsData = response.data;
      } else if (response.appointments && Array.isArray(response.appointments)) {
        appointmentsData = response.appointments;
      }



      const transformedDonors = await Promise.all(
        appointmentsData.map(async (appointment) => {
          try {
            let userInfo = {};
            const userId = appointment.UserId || appointment.userId || appointment.UserID;
            if (userId) {
              try {
                const userResponse = await bloodDonationService.getUserInfo(userId);
                userInfo = userResponse.data || userResponse;
              } catch (userError) {
                console.error(`Failed to fetch user info for userId ${userId}:`, userError);
                userInfo = {};
              }
            }

            return {
              id: appointment.AppointmentId || appointment.appointmentId || appointment.id,
              userId: userId,
              name: userInfo.Name || userInfo.name || appointment.Name || "N/A",
              phone: userInfo.Phone || userInfo.phone || appointment.Phone || "N/A",
              email: userInfo.Email || userInfo.email || appointment.Email || "N/A",
              // Priority 1: Get from Appointment table, Priority 2: Get from User table
              bloodType: (() => {
                // Try Appointment first
                if (appointment.BloodGroup && appointment.RhType) {
                  return `${appointment.BloodGroup}${appointment.RhType.replace('Rh', '')}`;
                }
                if (appointment.bloodGroup && appointment.rhType) {
                  return `${appointment.bloodGroup}${appointment.rhType.replace('Rh', '')}`;
                }
                // Then try User
                if (userInfo.BloodGroup && userInfo.RhType) {
                  return `${userInfo.BloodGroup}${userInfo.RhType.replace('Rh', '')}`;
                }
                if (userInfo.bloodGroup && userInfo.rhType) {
                  return `${userInfo.bloodGroup}${userInfo.rhType.replace('Rh', '')}`;
                }
                return "N/A";
              })(),
              bloodGroup: appointment.BloodGroup || appointment.bloodGroup || userInfo.BloodGroup || userInfo.bloodGroup,
              rhType: appointment.RhType || appointment.rhType || userInfo.RhType || userInfo.rhType,
              // Also try uppercase versions
              BloodGroup: appointment.BloodGroup || userInfo.BloodGroup,
              RhType: appointment.RhType || userInfo.RhType,
              age: userInfo.Age || userInfo.age || appointment.Age || 0,
              gender: userInfo.Gender || userInfo.gender || appointment.Gender || "unknown",
              weight: userInfo.Weight || userInfo.weight || appointment.Weight || 0,
              height: userInfo.Height || userInfo.height || appointment.Height || 0,
              appointmentDate: appointment.AppointmentDate || appointment.appointmentDate || appointment.RequestedDonationDate,
              timeSlot: appointment.TimeSlot || appointment.timeSlot || "morning",
              // Use actual database values for status and process
              status: appointment.Status !== undefined ? appointment.Status :
                appointment.status !== undefined ? appointment.status : 0,
              process: appointment.Process || appointment.process || 1,
              // Add cancel status fields
              cancel: appointment.Cancel !== undefined ? appointment.Cancel : appointment.cancel,
              Cancel: appointment.Cancel,
              cancelled: appointment.cancelled,
              isCancelled: appointment.Cancel === true || appointment.Cancel === 1 ||
                appointment.cancel === true || appointment.cancel === 1 ||
                appointment.cancelled === true,
              cancelledAt: appointment.CancelledAt || appointment.cancelledAt,
              CancelledAt: appointment.CancelledAt,
              cancelReason: appointment.CancelReason || appointment.cancelReason,
              CancelReason: appointment.CancelReason,
              lastDonationDate: appointment.LastDonationDate || appointment.lastDonationDate || userInfo.LastDonationDate,
              notes: appointment.Notes || appointment.notes || "",
              createdAt: appointment.CreatedAt || appointment.createdAt || new Date().toISOString(),
              // Add health information from appointment
              bloodPressure: appointment.BloodPressure || appointment.bloodPressure || "",
              heartRate: appointment.HeartRate || appointment.heartRate || 0,
              hemoglobin: appointment.Hemoglobin || appointment.hemoglobin || 0,
              temperature: appointment.Temperature || appointment.temperature || 0,
              doctorId: appointment.DoctorId || appointment.doctorId,
              healthSurvey: {
                chronicDiseases: [],
                recentIllness: false,
                medications: "",
                allergies: "",
              },
              testResults: {
                hemoglobin: "",
                bloodPressure: "",
                heartRate: "",
                temperature: "",
                weight: (userInfo.Weight || userInfo.weight || appointment.Weight || "").toString(),
                height: (userInfo.Height || userInfo.height || appointment.Height || "").toString(),
              },
              healthStatus: "unknown",
              bloodRelatedDiseases: [],
              totalDonations: 0,
            };
          } catch (transformError) {
            console.error("Error transforming appointment data:", transformError);
            return null;
          }
        })
      );

      const validDonors = transformedDonors.filter(donor => donor !== null);

      setDonors(validDonors);
    } catch (error) {
      console.error("Error loading donors:", error);
      message.error("Có lỗi xảy ra khi tải dữ liệu người hiến máu");
      setDonors([]);
    } finally {
      setLoading(false);
    }
  };

  // Filter donors based on multiple filters and sort by appointment date (nearest first)
  const getFilteredDonors = () => {
    const today = new Date().toISOString().split("T")[0];
    let filtered = donors;

    // Helper function to get status value (same as in statistics)
    const getStatusValue = (donor) => {
      // Try different status field names
      let status = donor.status;
      if (status === undefined || status === null) {
        status = donor.Status;
      }
      if (status === undefined || status === null) {
        status = donor.appointmentStatus;
      }
      if (status === undefined || status === null) {
        status = donor.AppointmentStatus;
      }

      // Convert to number if it's a string
      if (typeof status === 'string') {
        const parsed = parseInt(status);
        if (!isNaN(parsed)) {
          status = parsed;
        }
      }

      // Convert boolean to number (true = 2, false = 1)
      if (typeof status === 'boolean') {
        status = status ? 2 : 1;
      }

      // Default to 0 if still undefined
      return status !== undefined && status !== null ? status : 0;
    };



    // Apply status filter
    if (filters.status !== "all") {

      switch (filters.status) {
        case "today":
          filtered = filtered.filter((d) => {
            // Normalize appointment date for comparison
            const appointmentDate = d.appointmentDate || d.AppointmentDate || d.appointmentdate;
            if (!appointmentDate) return false;

            const normalizedDate = new Date(appointmentDate).toISOString().split('T')[0];
            return normalizedDate === today;
          });
          break;
        case "pending":
          filtered = filtered.filter((d) => {
            const status = getStatusValue(d);
            return status === 0;
          });
          break;
        case "approved":
          filtered = filtered.filter((d) => {
            const status = getStatusValue(d);

            return status === 2;
          });
          break;
        case "rejected":
          filtered = filtered.filter((d) => {
            const status = getStatusValue(d);
            return status === 1;
          });
          break;
        case "cancelled":
          filtered = filtered.filter((d) => {
            const status = getStatusValue(d);
            return status === 3;
          });
          break;
      }

    }

    // Apply blood type filter
    if (filters.bloodType !== "all") {

      filtered = filtered.filter((d) => {
        let bloodType = d.bloodType;

        // Handle different blood type field combinations
        if (d.bloodGroup && d.rhType) {
          const rhSymbol = d.rhType.includes('+') ? '+' :
            d.rhType.includes('-') ? '-' :
              d.rhType.replace('Rh', '');
          bloodType = `${d.bloodGroup}${rhSymbol}`;
        } else if (d.BloodGroup && d.RhType) {
          const rhSymbol = d.RhType.includes('+') ? '+' :
            d.RhType.includes('-') ? '-' :
              d.RhType.replace('Rh', '');
          bloodType = `${d.BloodGroup}${rhSymbol}`;
        }

        return bloodType === filters.bloodType;
      });

    }

    // Apply process filter
    if (filters.process !== "all") {

      filtered = filtered.filter((d) => {
        if (filters.process === "rejected") {
          return d.status === false || d.status === 1;
        }
        const currentProcess = d.process || d.Process || 1;
        return currentProcess === filters.process;
      });

    }

    // Apply time slot filter
    if (filters.timeSlot !== "all") {

      filtered = filtered.filter((d) => {
        const timeSlot = d.timeSlot || d.TimeSlot;
        if (!timeSlot) return false;

        // Handle different timeSlot formats
        if (filters.timeSlot === "morning") {
          return timeSlot === "morning" || timeSlot === "Sáng (7:00-12:00)";
        } else if (filters.timeSlot === "afternoon") {
          return timeSlot === "afternoon" || timeSlot === "Chiều (13:00-17:00)";
        }

        return timeSlot === filters.timeSlot;
      });

    }

    // Sort by appointment date (nearest first)
    const result = filtered.sort((a, b) => {
      const dateA = new Date(a.appointmentDate);
      const dateB = new Date(b.appointmentDate);
      return dateA - dateB; // Ascending order (nearest first)
    });

    return result;
  };

  // Calculate statistics
  const getStatistics = () => {
    const today = new Date();
    const todayStr = today.toISOString().split("T")[0]; // YYYY-MM-DD format

    // Helper function to normalize date for comparison
    const normalizeDate = (dateStr) => {
      if (!dateStr) return null;
      try {
        const date = new Date(dateStr);
        return date.toISOString().split("T")[0];
      } catch (error) {
        console.warn("Invalid date:", dateStr);
        return null;
      }
    };

    // Helper function to get status value
    const getStatusValue = (donor) => {
      // Try different status field names
      let status = donor.status;
      if (status === undefined || status === null) {
        status = donor.Status;
      }
      if (status === undefined || status === null) {
        status = donor.appointmentStatus;
      }
      if (status === undefined || status === null) {
        status = donor.AppointmentStatus;
      }

      // Convert to number if it's a string
      if (typeof status === 'string') {
        const parsed = parseInt(status);
        if (!isNaN(parsed)) {
          status = parsed;
        }
      }

      // Convert boolean to number (true = 2, false = 1)
      if (typeof status === 'boolean') {
        status = status ? 2 : 1;
      }

      // Default to 0 if still undefined
      return status !== undefined && status !== null ? status : 0;
    };

    const stats = {
      todayCount: donors.filter(d => {
        const appointmentDate = normalizeDate(d.appointmentDate || d.AppointmentDate || d.appointmentdate);
        return appointmentDate === todayStr;
      }).length,

      approvedCount: donors.filter(d => {
        const status = getStatusValue(d);
        return status === 2; // Approved
      }).length,

      rejectedCount: donors.filter(d => {
        const status = getStatusValue(d);
        return status === 1; // Rejected
      }).length,

      totalCount: donors.length
    };

    return stats;
  };

  // Handle update donor info
  const handleUpdateDonor = (donor) => {
    console.log("Opening update modal for donor:", donor);
    setSelectedDonor(donor);

    // Pre-fill form with existing donor data
    setUpdateData({
      bloodType: donor.bloodType || "",
      healthStatus: donor.healthStatus || "good", // Default to good if not set
      chronicDiseases: donor.healthSurvey?.chronicDiseases || [],
      bloodRelatedDiseases: donor.bloodRelatedDiseases || [],
      notes: donor.notes || "",
      bloodPressure: donor.bloodPressure || "",
      heartRate: donor.heartRate || 0,
      hemoglobin: donor.hemoglobin || 0,
      temperature: donor.temperature || 0,
      weight: donor.weight || 0,
      height: donor.height || 0,
      process: donor.process || 2,
      status: donor.status || false,
      donationCapacity: donor.donationCapacity || 0,
      testResults: {
        // Use existing values from donor or testResults
        hemoglobin: donor.testResults?.hemoglobin || "",
        bloodPressure: donor.testResults?.bloodPressure || "",
        heartRate: donor.testResults?.heartRate || "",
        temperature: donor.testResults?.temperature || "",
        weight: donor.weight || donor.testResults?.weight || "",
        height: donor.height || donor.testResults?.height || "",
      },
    });
    setShowUpdateModal(true);
  };

  // Handle update status
  const handleUpdateStatus = async (donor) => {
    try {
      setLoading(true);

      // Fetch latest appointment details from API
      const appointmentDetails = await bloodDonationService.getAppointmentDetails(donor.id);
      console.log("Fetched appointment details:", appointmentDetails);

      // Fetch user information (weight, height) from Information API
      let userInformation = null;
      try {
        userInformation = await bloodDonationService.getUserInformation(donor.userId);
        console.log("Fetched user information:", userInformation);
      } catch (infoError) {
        console.warn("Failed to fetch user information:", infoError);
      }

      // Use fresh data from API
      const updatedDonor = {
        ...donor,
        ...appointmentDetails,
        // Map API fields to expected format
        id: appointmentDetails.appointmentId || donor.id,
        userId: appointmentDetails.userId || donor.userId,
        status: appointmentDetails.status,
        process: appointmentDetails.process,
        notes: appointmentDetails.notes || "",
        bloodPressure: appointmentDetails.bloodPressure,
        heartRate: appointmentDetails.heartRate,
        hemoglobin: appointmentDetails.hemoglobin,
        temperature: appointmentDetails.temperature,
        doctorId: appointmentDetails.doctorId,
        appointmentDate: appointmentDetails.appointmentDate,
        timeSlot: appointmentDetails.timeSlot,
        cancel: appointmentDetails.cancel,
        createdAt: appointmentDetails.createdAt,
        // Add user information if available
        weight: userInformation?.weight || donor.weight,
        height: userInformation?.height || donor.height
      };

      setSelectedDonor(updatedDonor);

      // Fetch additional user information for weight/height
      let userInfo = {};
      try {
        userInfo = await bloodDonationService.getUserInfo(appointmentDetails.userId);
      } catch (error) {
        console.warn("Could not fetch user info:", error);
      }

      setStatusUpdateData({
        status: appointmentDetails.status ? "2" : "1", // Convert boolean to string
        process: appointmentDetails.process?.toString() || "2", // Default to step 2 (doctors start from step 2)
        notes: appointmentDetails.notes || "",
        bloodPressure: appointmentDetails.bloodPressure || "",
        heartRate: appointmentDetails.heartRate || 0,
        hemoglobin: appointmentDetails.hemoglobin || 0,
        temperature: appointmentDetails.temperature || 0,
        weight: userInfo.weight || appointmentDetails.weight || 0,
        height: userInfo.height || appointmentDetails.height || 0,
      });
      setShowStatusModal(true);
    } catch (error) {
      console.error("Error fetching appointment details:", error);
      // Fallback to using existing donor data
      setSelectedDonor(donor);
      setStatusUpdateData({
        status: donor.status || "",
        process: donor.process || "1",
        notes: donor.notes || "",
      });
      setShowStatusModal(true);
    } finally {
      setLoading(false);
    }
  };

  // Handle cancel appointment
  const handleDeleteAppointment = async (appointmentId) => {
    try {
      await bloodDonationService.deleteAppointment(appointmentId);

      // Update local state immediately for better UX
      setDonors(prev => prev.map(donor =>
        donor.id === appointmentId
          ? {
              ...donor,
              Cancel: true,
              cancel: true,
              cancelled: true,
              isCancelled: true,
              cancelledAt: new Date().toISOString(),
              CancelledAt: new Date().toISOString()
            }
          : donor
      ));

      // Reload data from server to ensure consistency
      await loadDonors();

      toast.success("Hủy lịch hẹn thành công!");
    } catch (error) {
      console.error("Error cancelling appointment:", error);
      toast.error("Có lỗi xảy ra khi hủy lịch hẹn!");
    }
  };

  // Load data on mount
  useEffect(() => {
    if (isBloodDepartment) {
      loadDonors();
    }
  }, [isBloodDepartment]);

  // Refresh data function - alias for loadDonors
  const refreshData = loadDonors;

  return {
    // State
    donors,
    loading,
    filters,
    selectedDonor,
    showUpdateModal,
    showStatusModal,
    updateData,
    statusUpdateData,
    currentUser,
    isBloodDepartment,

    // Computed values
    filteredDonors: getFilteredDonors(),
    statistics: getStatistics(),

    // Actions
    setFilters,
    setShowUpdateModal,
    setShowStatusModal,
    setUpdateData,
    setStatusUpdateData,
    setSelectedDonor,
    loadDonors,
    refreshData,
    handleUpdateDonor,
    handleUpdateStatus,
    handleDeleteAppointment,
  };
};
