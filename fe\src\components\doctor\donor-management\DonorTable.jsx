import { Table, Tag, Button } from "antd";
import { getGenderText, GENDER_FILTERS } from "../../../utils/genderUtils";

/**
 * Component bảng danh sách ng<PERSON>ời hiến máu
 */
const DonorTable = ({
  donors,
  loading,
  onUpdateDonor,
  onUpdateStatus,
  onDeleteAppointment
}) => {

  
  if (donors && donors.length > 0) {
    console.log('DonorTable - Sample donor data:', donors.slice(0, 3).map(d => ({
      id: d.id,
      name: d.name,
      Cancel: d.Cancel,
      cancel: d.cancel,
      cancelled: d.cancelled,
      isCancelled: d.isCancelled,
      status: d.status,
      process: d.process
    })));

    // Check if any donor has cancel = true
    const cancelledDonors = donors.filter(d =>
      d.Cancel === true || d.Cancel === 1 ||
      d.cancel === true || d.cancel === 1 ||
      d.cancelled === true || d.isCancelled === true
    );
    console.log('Found cancelled donors:', cancelledDonors.length, cancelledDonors.map(d => ({
      id: d.id,
      name: d.name,
      Cancel: d.Cancel,
      cancel: d.cancel,
      cancelled: d.cancelled,
      isCancelled: d.isCancelled,
      status: d.status,
      process: d.process
    })));

    // Log detailed info for cancelled donors
    cancelledDonors.forEach(d => {
      console.log(`Cancelled donor ${d.id} (${d.name}):`, {
        Cancel: d.Cancel,
        cancel: d.cancel,
        cancelled: d.cancelled,
        isCancelled: d.isCancelled,
        status: d.status,
        process: d.process,
        fullRecord: d
      });
    });
  }

  // Test: Add a fake cancelled donor to test the UI logic
  const testDonors = donors && donors.length > 0 ? [
    ...donors,
    {
      ...donors[0],
      id: 999,
      name: "Test Cancelled User",
      Cancel: true,
      cancel: true,
      cancelled: true,
      isCancelled: true,
      cancelledAt: new Date().toISOString(),
      cancelReason: "Test cancellation"
    }
  ] : donors;

  const getTimeSlotText = (slot) => {
    if (slot === "morning" || slot === "Sáng (7:00-12:00)") {
      return "7:00 - 12:00";
    } else if (slot === "afternoon" || slot === "Chiều (13:00-17:00)") {
      return "13:00 - 17:00";
    }
    return slot || "N/A";
  };

  const BLOOD_TYPES = [
    "O+", "O-", "A+", "A-", "B+", "B-", "AB+", "AB-"
  ];





  const TIMESLOT_FILTERS = [
    { text: "Sáng (7:00 - 12:00)", value: "morning" },
    { text: "Chiều (13:00 - 17:00)", value: "afternoon" },
  ];

  // Hàm sắp xếp ngày hẹn theo thứ tự: hôm nay, ngày mai, tương lai, trong quá khứ
  const sortAppointmentDate = (a, b) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    const dateA = new Date(a.appointmentDate);
    dateA.setHours(0, 0, 0, 0);

    const dateB = new Date(b.appointmentDate);
    dateB.setHours(0, 0, 0, 0);

    // Xác định loại ngày cho A
    let categoryA;
    if (dateA.getTime() === today.getTime()) {
      categoryA = 1; // Hôm nay
      // } else if (dateA.getTime() === tomorrow.getTime()) {
      //   categoryA = 2; // Ngày mai
    } else if (dateA > today) {
      categoryA = 3; // Tương lai
    } else {
      categoryA = 4; // Trong quá khứ
    }

    // Xác định loại ngày cho B
    let categoryB;
    if (dateB.getTime() === today.getTime()) {
      categoryB = 1; // Hôm nay
      // } else if (dateB.getTime() === tomorrow.getTime()) {
      //   categoryB = 2; // Ngày mai
    } else if (dateB > today) {
      categoryB = 3; // Tương lai
    } else {
      categoryB = 4; // Trong quá khứ
    }

    // Sắp xếp theo category trước
    if (categoryA !== categoryB) {
      return categoryA - categoryB;
    }

    // Nếu cùng category, sắp xếp theo ngày
    if (categoryA === 3) {
      // Tương lai: gần nhất trước
      return dateA - dateB;
    } else if (categoryA === 4) {
      // Quá khứ: gần nhất trước (ngày lớn nhất trước)
      return dateB - dateA;
    }

    // Hôm nay và ngày mai: không cần sắp xếp thêm
    return 0;
  };

  const columns = [
    {
      title: "Mã lịch hẹn",
      dataIndex: "id",
      key: "appointmentId",
      width: 100,
      render: (id) => <span style={{ fontWeight: 'bold', color: '#1890ff' }}>#{id}</span>,
      sorter: (a, b) => a.id - b.id,
    },
    {
      title: "Tên",
      dataIndex: "name",
      key: "name",
    },
    {
      title: "Nhóm máu",
      dataIndex: "bloodType",
      key: "bloodType",
      render: (bloodType, record) => {
        // Try to combine BloodGroup and RhType if available, otherwise use bloodType
        let displayBloodType = bloodType;

        if (record.bloodGroup && record.rhType) {
          // If we have separate fields, combine them
          const rhSymbol = record.rhType.includes('+') ? '+' :
            record.rhType.includes('-') ? '-' :
              record.rhType.replace('Rh', '');
          displayBloodType = `${record.bloodGroup}${rhSymbol}`;
        } else if (record.BloodGroup && record.RhType) {
          // Try uppercase field names
          const rhSymbol = record.RhType.includes('+') ? '+' :
            record.RhType.includes('-') ? '-' :
              record.RhType.replace('Rh', '');
          displayBloodType = `${record.BloodGroup}${rhSymbol}`;
        }

        return <Tag color="red">{displayBloodType || 'N/A'}</Tag>;
      },
      filters: BLOOD_TYPES.map(type => ({ text: type, value: type })),
      onFilter: (value, record) => {
        // Check multiple possible field combinations
        let bloodTypeToCheck = record.bloodType;

        if (record.bloodGroup && record.rhType) {
          const rhSymbol = record.rhType.includes('+') ? '+' :
            record.rhType.includes('-') ? '-' :
              record.rhType.replace('Rh', '');
          bloodTypeToCheck = `${record.bloodGroup}${rhSymbol}`;
        } else if (record.BloodGroup && record.RhType) {
          const rhSymbol = record.RhType.includes('+') ? '+' :
            record.RhType.includes('-') ? '-' :
              record.RhType.replace('Rh', '');
          bloodTypeToCheck = `${record.BloodGroup}${rhSymbol}`;
        }

        return bloodTypeToCheck === value;
      },
    },
    {
      title: "Điện thoại",
      dataIndex: "phone",
      key: "phone",
    },
    {
      title: "Email",
      dataIndex: "email",
      key: "email",
    },
    {
      title: "Ngày đăng ký",
      dataIndex: "createdAt",
      key: "createdAt",
      render: (d) => (d ? new Date(d).toLocaleDateString("vi-VN") : "N/A"),
      sorter: (a, b) => new Date(a.createdAt) - new Date(b.createdAt),
    },
    {
      title: "Ngày hẹn",
      dataIndex: "appointmentDate",
      key: "appointmentDate",
      render: (d) => {
        if (!d) return "N/A";

        const appointmentDate = new Date(d);
        const today = new Date();
        today.setHours(0, 0, 0, 0);
        appointmentDate.setHours(0, 0, 0, 0);

        const tomorrow = new Date(today);
        tomorrow.setDate(tomorrow.getDate() + 1);

        let label = "";
        if (appointmentDate.getTime() === today.getTime()) {
          label = " (Hôm nay)";
        }

        return (
          <span>
            {new Date(d).toLocaleDateString("vi-VN")}
            {label && <span style={{ color: '#1890ff', fontWeight: 'bold' }}>{label}</span>}
          </span>
        );
      },
      sorter: sortAppointmentDate,
      defaultSortOrder: 'ascend',
    },
    {
      title: "Quy trình hiến máu",
      dataIndex: "process",
      key: "process",
      render: (process, record) => {
        // Check if cancelled by user - prioritize Cancel field from Appointment table
        const isCancelled = record.Cancel === true || record.Cancel === 1 ||
          record.cancel === true || record.cancel === 1 ||
          record.cancelled === true || record.isCancelled === true;

        // Debug specific record
        if (record.id === 19) {
          console.log('Record 19 debug:', {
            id: record.id,
            Cancel: record.Cancel,
            cancel: record.cancel,
            cancelled: record.cancelled,
            isCancelled: record.isCancelled,
            calculatedIsCancelled: isCancelled,
            status: record.status,
            process: record.process
          });
        }

        // Check if rejected by doctor (status = false or 1)
        const isRejected = record.status === false || record.status === 1;

        if (isCancelled) {
          // Show cancelled status - always show as cancelled by user since only users can cancel
          return (
            <div>
              <Tag color="volcano">Đã hủy bởi người đăng ký</Tag>
              {(record.cancelledAt || record.CancelledAt) && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px', maxWidth: '200px' }}>
                  Hủy lúc: {new Date(record.cancelledAt || record.CancelledAt).toLocaleString("vi-VN")}
                </div>
              )}
              {(record.cancelReason || record.CancelReason) && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px', maxWidth: '200px' }}>
                  Lý do: {record.cancelReason || record.CancelReason}
                </div>
              )}
            </div>
          );
        } else if (isRejected) {
          // Show rejection with notes only when rejected by doctor
          return (
            <div>
              <Tag color="red">Không chấp nhận</Tag>
              {(record.notes || record.Notes) && (
                <div style={{ fontSize: '12px', color: '#666', marginTop: '2px', maxWidth: '200px' }}>
                  {record.notes || record.Notes}
                </div>
              )}
            </div>
          );
        } else {
          // Show process steps for all other cases (approved, pending, etc.)
          const processSteps = {
            1: "Đăng ký",
            2: "Khám sức khỏe cơ bản",
            3: "Lấy máu",
            4: "Xét nghiệm máu",
            5: "Nhập kho"
          };

          // Use actual process from record, not default
          const currentProcess = record.process || record.Process || process || 1;
          const processText = processSteps[currentProcess] || "Đăng ký";

          // Color coding for different steps
          const stepColors = {
            1: "blue",      // Đăng ký
            2: "orange",    // Khám sức khỏe cơ bản
            3: "purple",    // Lấy máu
            4: "cyan",      // Xét nghiệm máu
            5: "green"      // Nhập kho
          };

          return (
            <Tag color={stepColors[currentProcess] || "blue"}>
              Bước {currentProcess}: {processText}
            </Tag>
          );
        }
      },
      filters: [
        { text: "Đăng ký", value: 1 },
        { text: "Khám sức khỏe cơ bản", value: 2 },
        { text: "Lấy máu", value: 3 },
        { text: "Xét nghiệm máu", value: 4 },
        { text: "Nhập kho", value: 5 },
        { text: "Không chấp nhận", value: "rejected" },
        { text: "Đã hủy bởi người đăng ký", value: "cancelled" },
      ],
      onFilter: (value, record) => {
        // Check if cancelled by user
        const isCancelled = record.Cancel === true || record.Cancel === 1 ||
          record.cancel === true || record.cancel === 1 ||
          record.cancelled === true || record.isCancelled === true;

        if (value === "cancelled") {
          return isCancelled;
        }

        if (value === "rejected") {
          return !isCancelled && (record.status === false || record.status === 1);
        }

        // For process filters, exclude cancelled appointments
        if (!isCancelled && !(record.status === false || record.status === 1)) {
          const currentProcess = record.process || record.Process || 1;
          return currentProcess === value;
        }

        return false;
      },
    },
    {
      title: "Giờ hẹn",
      dataIndex: "timeSlot",
      key: "timeSlot",
      render: (t) => <Tag color="blue">{getTimeSlotText(t)}</Tag>,
      filters: TIMESLOT_FILTERS,
      onFilter: (value, record) => record.timeSlot === value,
    },
    {
      title: "Hành động",
      key: "actions",
      render: (_, donor) => {
        // Check if cancelled by user - use consistent logic with process column
        const isCancelled = donor.Cancel === true || donor.Cancel === 1 ||
          donor.cancel === true || donor.cancel === 1 ||
          donor.cancelled === true || donor.isCancelled === true;

        return (
          <div style={{ display: 'flex', gap: '4px', flexWrap: 'wrap' }}>
            <Button
              type="link"
              size="small"
              onClick={() => onUpdateDonor(donor)}
              disabled={isCancelled}
              style={isCancelled ? { opacity: 0.5 } : {}}
              title={isCancelled ? "Không thể xem thông tin lịch hẹn đã hủy" : "Xem thông tin chi tiết"}
            >
              Thông tin
            </Button>
            <Button
              type="link"
              size="small"
              onClick={() => onUpdateStatus(donor)}
              disabled={isCancelled}
              style={isCancelled ? { opacity: 0.5 } : {}}
              title={isCancelled ? "Không thể cập nhật trạng thái lịch hẹn đã hủy" : "Cập nhật trạng thái"}
            >
              Trạng thái
            </Button>
            
          </div>
        );
      },
    },
  ];

  return (
    <Table
      dataSource={testDonors}
      columns={columns}
      rowKey="id"
      loading={loading}
      pagination={{ pageSize: 8 }}
      sortDirections={['ascend', 'descend']}
    />
  );
};

export default DonorTable;
